from enum import Enum
from typing import Optional, Dict, Any
from managers.logger_manager import logger
import asyncio

class ErrorType(Enum):
    HARDWARE = "hardware"
    TIMEOUT = "timeout"
    WEBSOCKET = "websocket"
    SESSION = "session"
    VALIDATION = "validation"
    UNKNOWN = "unknown"

class ErrorCode(Enum):
    # Hardware errors
    UNLOCK_FAILED = "ERR_UNLOCK"
    LOCK_FAILED = "ERR_LOCK"
    DOOR_TIMEOUT = "ERR_DOOR_TIMEOUT"
    TRANSMISSION_TIMEOUT = "ERR_TRANSMISSION"
    
    # Session errors
    INVALID_SESSION = "ERR_INVALID_SESSION"
    SESSION_EXPIRED = "ERR_SESSION_EXPIRED"
    
    # WebSocket errors
    WS_DISCONNECT = "ERR_WS_DISCONNECT"
    WS_TIMEOUT = "ERR_WS_TIMEOUT"
    
    # Validation errors
    INVALID_PIN = "ERR_INVALID_PIN"
    INVALID_SECTION = "ERR_INVALID_SECTION"
    
    # General errors
    UNKNOWN = "ERR_UNKNOWN"

class SectionError(Exception):
    def __init__(
        self,
        message: str,
        error_type: ErrorType,
        error_code: ErrorCode,
        section_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_type = error_type
        self.error_code = error_code
        self.section_id = section_id
        self.details = details or {}
        super().__init__(self.message)

class ErrorManager:
    def __init__(self):
        self.retry_limits = {
            ErrorType.HARDWARE: 2,
            ErrorType.WEBSOCKET: 3,
            ErrorType.TIMEOUT: 1
        }
        self.retry_delays = { 
            ErrorType.HARDWARE: 2, 
            ErrorType.WEBSOCKET: 1,
            ErrorType.TIMEOUT: 1
        }
        
    async def handle_error(
        self,
        error: SectionError,
        retry_count: int = 0
    ) -> Dict[str, Any]:
        """
        Process error and try to recover if possible
        """
        logger.error(f"Handling error: {error.error_code} ({error.error_type})")
        logger.error(f"Details: {error.details}")
        
        # Basic response structure
        response = {
            "success": False,
            "error_code": error.error_code.value,
            "message": error.message,
            "should_retry": False,
            "retry_after": None
        }
        
        # Check retry limit
        max_retries = self.retry_limits.get(error.error_type, 0)
        if retry_count >= max_retries:
            logger.warning(f"Max retries ({max_retries}) reached for {error.error_code}")
            return response
        
        # Process error type
        if error.error_type == ErrorType.HARDWARE:
            return await self._handle_hardware_error(error, retry_count, response)
        elif error.error_type == ErrorType.TIMEOUT:
            return await self._handle_timeout_error(error, retry_count, response)
        elif error.error_type == ErrorType.WEBSOCKET:
            return await self._handle_websocket_error(error, retry_count, response)
        elif error.error_type == ErrorType.SESSION:
            return await self._handle_session_error(error, response)
        else:
            return response

    async def _handle_hardware_error(
        self,
        error: SectionError,
        retry_count: int,
        response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process hardware errors with retry mechanism
        """
        if error.error_code == ErrorCode.TRANSMISSION_TIMEOUT:
            # For timeout transmission try immediate retry
            response["should_retry"] = True
            response["retry_after"] = 0
        else:
            # For other hardware errors wait between retries
            delay = self.retry_delays[ErrorType.HARDWARE]
            response["should_retry"] = True
            response["retry_after"] = delay
            
        return response

    async def _handle_timeout_error(
        self,
        error: SectionError,
        retry_count: int,
        response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process timeout errors
        """
        if error.error_code == ErrorCode.DOOR_TIMEOUT:
            # Door timeout has no retry
            response["should_retry"] = False
            response["message"] = "Vypršel časový limit pro operaci s dvířky"
        else:
            delay = self.retry_delays[ErrorType.TIMEOUT]
            response["should_retry"] = True
            response["retry_after"] = delay
            
        return response

    async def _handle_websocket_error(
        self,
        error: SectionError,
        retry_count: int,
        response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process WebSocket errors
        """
        delay = self.retry_delays[ErrorType.WEBSOCKET]
        response["should_retry"] = True
        response["retry_after"] = delay
        response["should_use_polling"] = True
        return response

    async def _handle_session_error(
        self,
        error: SectionError,
        response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process session errors
        """
        # Session errors have no retry
        response["should_retry"] = False
        if error.error_code == ErrorCode.SESSION_EXPIRED:
            response["should_revalidate"] = True
        return response

error_manager = ErrorManager()
