-- migrate:up
CREATE TABLE timeline_log (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    serial_number VA<PERSON>HAR(255) DEFAULT NULL,
    order_number VA<PERSON>HAR(255) NULL,
    entered_pin VARCHAR(50) DEFAULT NULL,
    event_type VARCHAR(50) DEFAULT NULL,
    event_result VARCHAR(255) DEFAULT NULL,
    operator_id VARCHAR(255) DEFAULT NULL,
    section_id INT(11) DEFAULT NULL,
    tempered_unlock INT(11) DEFAULT NULL,
    box_status VARCHAR(255) DEFAULT NULL,
    message VARCHAR(255) DEFAULT NULL,
    mode VARCHAR(255) DEFAULT NULL,
    session_id VARCHAR(50) DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE timeline_log;