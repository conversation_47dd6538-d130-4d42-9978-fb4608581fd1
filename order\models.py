"""
Order domain models and schemas.
Defines request/response models for order endpoints.
"""

from pydantic import BaseModel, Field
from typing import List, Optional

# Request Models
class EmploymentPickupExpiredRequest(BaseModel):
    operator_id: int = Field(..., description="Operator ID who is picking up the orders")

class EmploymentPickupRequest(BaseModel):
    operator_id: int = Field(..., description="Operator ID who is picking up the orders")

class EmploymentDeliverRequest(BaseModel):
    phone_number: str = Field(..., description="Employee's phone number")

class OrderDeliverRequest(BaseModel):
    pass  # No parameters needed for operator deliver endpoint

class CheckAfterDeliveryRequest(BaseModel):
    pass  # No parameters needed for check after delivery endpoint

class EmploymentSendRequest(BaseModel):
    phone_number: str = Field(..., description="Employee's phone number")

class CustomerPickupRequest(BaseModel):
    pickup_pin: str = Field(..., description="Pickup PIN")

class CustomerReclaimRequest(BaseModel):
    reclamation_pin: str = Field(..., description="Reclamation PIN")

class CustomerSendRequest(BaseModel):
    reservation_pin: str = Field(..., description="Reservation PIN")

class CreateReservationRequest(BaseModel):
    session_id: str = Field(..., description="Session ID")
    type: str = Field(..., description="Type of reservation to create")
    sections: List[int] = Field(..., description="List of selected sections")

# Response Models
class EmploymentPickupExpiredResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    sections: List[int] = Field(default_factory=list, description="All sections with expired orders")
    total_sections: int = Field(0, description="Total number of sections with expired orders")

class EmploymentPickupResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    sections: List[int] = Field(default_factory=list, description="All sections with employee's orders")
    total_sections: int = Field(0, description="Total number of sections with employee's orders")

class EmploymentDeliverResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    sections: Optional[List[int]] = Field(None, description="Reserved section IDs from jetveo API")

class OrderDeliverResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    total_reservations: int = Field(0, description="Total number of reservations to deliver")

class CheckAfterDeliveryResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID for pickup if needed")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    sections: List[int] = Field(default_factory=list, description="Sections that need pickup")
    total_sections: int = Field(0, description="Total number of sections needing pickup")
    needs_pickup: bool = Field(False, description="Whether pickup process is needed")
    completed_orders: int = Field(0, description="Number of orders that were completed")

class EmploymentSendResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    section_id: Optional[int] = Field(None, description="Reserved section ID")
    valid: bool = Field(..., description="Whether phone number is valid")
    message: str = Field(..., description="Success or error message")

class CustomerPickupResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    section_id: Optional[int] = Field(None, description="Section ID for pickup")
    requires_payment: bool = Field(False, description="Whether payment is required")
    amount: float = Field(0.0, description="Amount to pay if payment required")

class CustomerReclaimResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    section_id: Optional[int] = Field(None, description="Reserved section ID")
    valid: bool = Field(..., description="Whether reclamation PIN is valid")
    message: str = Field(..., description="Success or error message")

class CustomerSendResponse(BaseModel):
    session_id: Optional[str] = Field(None, description="WebSocket session ID")
    success: bool = Field(..., description="Whether the operation was successful")
    section_id: Optional[int] = Field(None, description="Reserved section ID if exists")
    message: str = Field(..., description="Success or error message")

class CreateReservationResponse(BaseModel):
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")

class OrderPackage(BaseModel):
    package_pin: str = Field(..., description="Package PIN")
    section_id: int = Field(..., description="Section ID")
    status: int = Field(..., description="Package status")

class OrderListItem(BaseModel):
    insert_pin: str = Field(..., description="Insert PIN for the order")
    packages: List[OrderPackage] = Field(..., description="List of packages in this order")

class ListOrdersResponse(BaseModel):
    orders: List[OrderListItem] = Field(..., description="List of orders")

# Internal Models
class OrderReservation(BaseModel):
    id: int
    uuid: str
    box_uuid: Optional[str] = None
    section_id: Optional[str] = None
    status: int
    insert_pin: Optional[str] = None
    pickup_pin: Optional[str] = None
    phone_number: Optional[str] = None
    size_category: Optional[int] = None
    expired: Optional[int] = None
    type: Optional[str] = None
    last_update: str
    created_at: str

# WebSocket Message Models
class WebSocketMessage(BaseModel):
    type: str
    data: Optional[dict] = None

class HardwareMessage(BaseModel):
    type: str
    section_id: Optional[int] = None
    status: Optional[str] = None
    inserted: Optional[bool] = None

class OrderDeliverMessage(BaseModel):
    order_deliver: bool
    message: Optional[str] = None

class OrderSendMessage(BaseModel):
    order_send: bool
    message: Optional[str] = None
