from managers.logger_manager import logger
import aiohttp
from typing import Dict, Optional, Any
from datetime import datetime

from .models import (
    TransactionType, 
    TransactionState,
    TransactionStatus, 
    TransactionCallback,
    TransactionContext,
    TransactionEvent
)
from managers import session_manager, SessionType, SessionStatus, ws_manager
from managers.session_manager import SectionConfig

class TransactionManager:
    """Manager pro různé typy transakcí"""
    
    def __init__(self):
        self.external_services = {
            "payment": "http://localhost:8080/",
        }
        self.active_transactions: Dict[str, TransactionContext] = {}
    
    async def start_transaction(
        self,
        transaction_type: TransactionType,
        session_id: str,
        **transaction_data
    ) -> bool:
        """Spustí transakci podle typu"""
        
        logger.info(f"Starting transaction: type={transaction_type}, session={session_id}, data={transaction_data}")
        
        try:
            # Create session in session_manager first
            session = session_manager.create_session(
                session_type=SessionType.TRANSACTION,
                session_id=session_id,  # Use the same session_id
                status=SessionStatus.PENDING,
                transaction_data={
                    "transaction_type": transaction_type,
                    **transaction_data
                }
            )
            
            # Get or create transaction context
            context = self.active_transactions.get(session_id)
            if not context:
                logger.info("Creating new transaction context")
                context = TransactionContext(
                    session_id=session_id,
                    current_state=TransactionState.INIT,
                    transaction_type=transaction_type,
                    created_at=datetime.now(),
                    last_updated=datetime.now(),
                    metadata=transaction_data
                )
                self.active_transactions[session_id] = context
            
            # Add event about transaction start
            context.add_event(
                "transaction_started",
                data={"type": transaction_type.value}
            )
            
            # Initialize transaction data but don't start processing yet
            if transaction_type == TransactionType.PAYMENT:
                amount = transaction_data.get("amount")
                section_id = transaction_data.get("section_id")
                
                if not amount or not section_id:
                    error_msg = f"Missing amount or section_id for payment"
                    context.add_event("error", error=error_msg)
                    context.current_state = TransactionState.ERROR
                    raise ValueError(error_msg)
                
                # Just prepare payment data
                context.payment_data = {
                    "amount": float(amount),
                    "section_id": str(section_id),
                    "transaction_id": transaction_data.get("transaction_id", f"payment_{section_id}")
                }
                
                # Set state to pending - actual payment will start after WebSocket connection
                context.current_state = TransactionState.PAYMENT_PENDING
                return True
                
            elif transaction_type == TransactionType.STORAGE:
                return await self._handle_storage_transaction(session_id, context)
            elif transaction_type == TransactionType.PICKUP:
                return await self._handle_pickup_transaction(session_id, context)
            else:
                error_msg = f"Unknown transaction type: {transaction_type}"
                logger.error(error_msg)
                context.add_event("error", error=error_msg)
                context.current_state = TransactionState.ERROR
                return False
                
        except Exception as e:
            logger.error(f"Error starting {transaction_type} transaction: {e}")
            if session_id in self.active_transactions:
                context = self.active_transactions[session_id]
                context.add_event("error", error=str(e))
                context.current_state = TransactionState.ERROR
            
            await self._send_ws_message(session_id, {
                "type": "transaction_error",
                "message": f"Transaction failed: {str(e)}"
            })
            return False

    async def start_payment_processing(self, session_id: str) -> bool:
        """Zahájí zpracování platby po připojení WebSocketu"""
        try:
            context = self.active_transactions.get(session_id)
            if not context or context.current_state != TransactionState.PAYMENT_PENDING:
                return False

            # Send payment request
            payment_data = {
                "type": "sale",
                "amount": float(context.payment_data["amount"]),
                "variable_symbol": str(context.payment_data["section_id"])
            }
            
            logger.info(f"Starting payment processing: {payment_data}")
            context.add_event("payment_request_sent", data=payment_data)
            
            async with aiohttp.ClientSession() as http_session:
                try:
                    async with http_session.post(
                        self.external_services["payment"],
                        json=payment_data,
                        timeout=30
                    ) as response:
                        response_text = await response.text()
                        logger.info(f"Payment service response: {response.status} - {response_text}")
                        
                        if response.status == 200:
                            context.current_state = TransactionState.PAYMENT_PROCESSING
                            context.add_event("payment_processing")
                            
                            # Notify client
                            await self._send_ws_message(session_id, {
                                "type": "payment_status",
                                "status": "processing",
                                "message": "Pokračujte na platebním terminálu"
                            })
                            return True
                        else:
                            error_msg = f"Payment service returned status {response.status}"
                            context.add_event("error", error=error_msg)
                            context.current_state = TransactionState.ERROR
                            raise Exception(error_msg)
                            
                except aiohttp.ClientError as e:
                    error_msg = f"Error connecting to payment service: {str(e)}"
                    context.add_event("error", error=error_msg)
                    context.current_state = TransactionState.ERROR
                    raise Exception(error_msg)
                        
        except Exception as e:
            logger.error(f"Payment processing error: {e}")
            if session_id in self.active_transactions:
                context = self.active_transactions[session_id]
                context.current_state = TransactionState.ERROR
                context.add_event("error", error=str(e))
            
            await self._send_ws_message(session_id, {
                "type": "payment_status",
                "status": "error",
                "message": f"Chyba při zpracování platby: {str(e)}"
            })
            return False
    
    async def _handle_payment_transaction(
        self, 
        session_id: str,
        context: TransactionContext
    ) -> bool:
        """Zpracuje platební transakci"""
        try:
            amount = context.metadata.get("amount")
            section_id = context.metadata.get("section_id")
            
            logger.info(f"Processing payment transaction: amount={amount}, section={section_id}")
            
            if not amount or not section_id:
                error_msg = f"Missing amount or section_id for payment"
                context.add_event("error", error=error_msg)
                context.current_state = TransactionState.ERROR
                raise ValueError(error_msg)
            
            # Update payment data
            context.payment_data = {
                "amount": float(amount),
                "section_id": str(section_id),
                "transaction_id": context.metadata.get("transaction_id", f"payment_{section_id}")
            }
            
            # Send payment request
            payment_data = {
                "type": "sale",
                "amount": float(amount),
                "variable_symbol": str(section_id)
            }
            
            logger.info(f"Sending payment request: {payment_data}")
            context.add_event("payment_request_sent", data=payment_data)
            
            async with aiohttp.ClientSession() as http_session:
                try:
                    async with http_session.post(
                        self.external_services["payment"],
                        json=payment_data,
                        timeout=30
                    ) as response:
                        response_text = await response.text()
                        logger.info(f"Payment service response: {response.status} - {response_text}")
                        
                        if response.status == 200:
                            context.current_state = TransactionState.PAYMENT_PROCESSING
                            context.add_event("payment_processing")
                            
                            # Notify client
                            await self._send_ws_message(session_id, {
                                "type": "payment_status",
                                "status": "processing",
                                "message": "Pokračujte na platebním terminálu"
                            })
                            return True
                        else:
                            error_msg = f"Payment service returned status {response.status}"
                            context.add_event("error", error=error_msg)
                            context.current_state = TransactionState.ERROR
                            raise Exception(error_msg)
                            
                except aiohttp.ClientError as e:
                    error_msg = f"Error connecting to payment service: {str(e)}"
                    context.add_event("error", error=error_msg)
                    context.current_state = TransactionState.ERROR
                    raise Exception(error_msg)
                        
        except Exception as e:
            logger.error(f"Payment transaction error: {e}")
            context.current_state = TransactionState.ERROR
            await self._send_ws_message(session_id, {
                "type": "payment_status",
                "status": "error",
                "message": f"Chyba při zahájení platby: {str(e)}"
            })
            return False
    
    async def _handle_storage_transaction(self, session_id: str, session, **data) -> bool:
        """Zpracuje transakci uložení - placeholder pro budoucí implementaci"""
        logger.warning(f"Storage transaction not implemented yet for session {session_id}")
        return False
    
    async def _handle_pickup_transaction(self, session_id: str, session, **data) -> bool:
        """Zpracuje transakci vyzvednutí bez platby - placeholder pro budoucí implementaci"""
        logger.warning(f"Pickup transaction not implemented yet for session {session_id}")
        return False
    
    async def handle_external_callback(self, callback: TransactionCallback) -> bool:
        """Zpracuje callback z externího systému"""
        try:
            # Najdeme aktivní transakci podle transaction_id
            context = None
            for tx_context in self.active_transactions.values():
                if tx_context.payment_data and \
                   tx_context.payment_data.get("transaction_id") == callback.transaction_id:
                    context = tx_context
                    break
            
            if not context:
                logger.error(f"No transaction found for ID: {callback.transaction_id}")
                return False
            
            if callback.status == "success":
                return await self._handle_successful_callback(context, callback)
            else:
                return await self._handle_failed_callback(context, callback)
                
        except Exception as e:
            logger.error(f"Error handling external callback: {e}")
            return False
    
    async def _check_websocket(self, session_id: str) -> bool:
        """Zkontroluje jestli je WebSocket spojení aktivní"""
        return session_id in ws_manager.connections

    async def _send_ws_message(self, session_id: str, message: Dict) -> bool:
        """Bezpečně pošle zprávu přes WebSocket"""
        try:
            if await self._check_websocket(session_id):
                await ws_manager.send(session_id, message)
                return True
            else:
                logger.warning(f"WebSocket not connected for session {session_id}")
                return False
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            return False

    async def _handle_successful_callback(
        self, 
        context: TransactionContext, 
        callback: TransactionCallback
    ) -> bool:
        """Zpracuje úspěšný callback"""
        try:
            session_id = context.session_id
            logger.info(f"Processing successful callback for session {session_id}")
            
            # Update state
            context.current_state = TransactionState.PAYMENT_COMPLETE
            context.add_event("payment_completed", data=callback.data)
            logger.info(f"Updated state to PAYMENT_COMPLETE for session {session_id}")
            
            # Notify client
            await self._send_ws_message(session_id, {
                "type": "payment_status",
                "status": "success",
                "message": "Platba byla úspěšná"
            })
            
            # Update DB status
            await self._update_payment_status(context)
            logger.info(f"Updated payment status in DB for session {session_id}")
            
            # Prepare hardware
            context.current_state = TransactionState.HARDWARE_PREPARING
            context.add_event("hardware_preparing")
            logger.info(f"Starting hardware preparation for session {session_id}")
            
            await self._send_ws_message(session_id, {
                "type": "status",
                "message": "Připravuji otevření schránky..."
            })
            
            # Initialize hardware
            success = await self._prepare_hardware(context)
            logger.info(f"Hardware preparation result: {success} for session {session_id}")
            
            if success:
                context.current_state = TransactionState.HARDWARE_READY
                context.add_event("hardware_ready")
                logger.info(f"Hardware ready for session {session_id}")
                
                await self._send_ws_message(session_id, {
                    "type": "hardware_status",
                    "status": "ready",
                    "message": "Schránka je připravena"
                })
            else:
                raise Exception("Failed to prepare hardware")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in successful callback handler: {e}")
            context.current_state = TransactionState.ERROR
            context.add_event("error", error=str(e))
            
            await self._send_ws_message(context.session_id, {
                "type": "error",
                "message": "Chyba při zpracování platby"
            })
            return False
    
    async def _handle_failed_callback(self, session, callback: TransactionCallback) -> bool:
        """Zpracuje neúspěšný callback"""
        session_id = session.session_id
        transaction_type = session.transaction_data.get("transaction_type")
        
        # Informujeme zákazníka o neúspěchu
        if transaction_type == TransactionType.PAYMENT:
            await ws_manager.send(session_id, {
                "message": "Platba byla zamítnuta"
            })
        else:
            await ws_manager.send(session_id, {
                "message": f"Transakce {transaction_type} se nezdařila"
            })
        
        # Ukončíme session
        await session_manager.remove_session(session_id)
        return True
    
    async def _update_payment_status(self, context: TransactionContext):
        """Aktualizuje stav platby v DB"""
        try:
            from infrastructure.repositories.product_repository import product_repository
            
            section_id = context.payment_data["section_id"]
            
            # Use new universal function to update payment status
            result = await product_repository.edit_reservation(
                section_id=section_id,
                paid_status='1',
                status_to_set=0,   # Set to inactive after payment
                action=0  # Action code 0 for purchased
            )

            if not result:
                logger.error(f"Failed to update payment status for section {section_id}")
                raise Exception(f"Failed to update payment status for section {section_id}")
            
            logger.info(f"Updated payment status for section {section_id}")
            
        except Exception as e:
            logger.error(f"Error updating payment status: {e}")

    async def _prepare_hardware(self, context: TransactionContext) -> bool:
        """Připraví hardware pro výdej - placeholder pro budoucí implementaci"""
        try:
            logger.info(f"Preparing hardware for session {context.session_id}")
            section_id = context.payment_data["section_id"]
            
            # Placeholder - hardware preparation will be implemented later
            context.hardware_data = {
                "section_id": section_id,
                "status": "ready"
            }
            
            logger.info(f"Hardware prepared successfully for session {context.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error preparing hardware: {e}")
            return False

    async def open_locker_for_pickup(self, session_id: str) -> bool:
        """Otevře schránku pro vyzvednutí produktu"""
        try:
            logger.info(f"Starting open_locker_for_pickup for session {session_id}")
            
            context = self.active_transactions.get(session_id)
            if not context or context.current_state != TransactionState.HARDWARE_READY:
                logger.error(f"Cannot open locker - invalid state: {context.current_state if context else 'No context'}")
                return False

            # Get hardware configuration for the section
            from domains.product.service import product_service
            section_id = context.payment_data["section_id"]
            logger.info(f"Getting hardware config for section {section_id}")
            
            hardware_config = await product_service.get_hardware_config_for_section(int(section_id))
            
            if not hardware_config:
                logger.error(f"No hardware configuration found for section {section_id}")
                return False

            logger.info(f"Hardware config: {hardware_config}")

            # Update state
            context.current_state = TransactionState.HARDWARE_ACTIVE
            context.add_event("hardware_operation_started")
            context.hardware_data = {
                "section_id": section_id,
                "status": "opening",
                "hardware_config": {
                    "lock_id": hardware_config.lock_id,
                    "is_tempered": hardware_config.is_tempered,
                    "led_section": hardware_config.led_section
                }
            }

            # Notify client
            await self._send_ws_message(session_id, {
                "type": "hardware_status",
                "status": "opening",
                "message": "Otevírám schránku..."
            })

            # Start hardware sequence
            from managers import sequence_manager
            logger.info(f"Starting FSM sequence for session {session_id} with config {hardware_config}")
            
            success = await sequence_manager.start_fsm_sequence(
                session_id=session_id,
                sections=[hardware_config],
                pin="transaction_pickup"
            )

            if not success:
                logger.error(f"Failed to start hardware sequence for session {session_id}")
                context.current_state = TransactionState.ERROR
                context.add_event("error", error="Failed to start hardware sequence")
                
                await self._send_ws_message(session_id, {
                    "type": "hardware_status",
                    "status": "error",
                    "message": "Chyba při otevírání schránky"
                })
                return False

            logger.info(f"Hardware sequence started successfully for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Error opening locker: {e}")
            if session_id in self.active_transactions:
                context = self.active_transactions[session_id]
                context.current_state = TransactionState.ERROR
                context.add_event("error", error=str(e))
            
            await self._send_ws_message(session_id, {
                "type": "hardware_status",
                "status": "error",
                "message": f"Chyba při otevírání schránky: {str(e)}"
            })
            return False

# Global instance
transaction_manager = TransactionManager()
