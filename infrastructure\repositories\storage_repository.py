import mysql.connector
from os import getenv
from typing import List, Dict, Any, Tuple, Optional
from managers.timeline_logger import log_timeline_event
from managers.logger_manager import logger
from infrastructure.external_apis.jetveo_client import storage_change_status_async

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class StorageRepository:
    def __init__(self):
        self.time_status_doesnt_change = 15     # time, in which time does not change in minutes
        self.time_reservation_cancel = 48       # time in hours after which PIN changes and SMS is sent

    def get_all_categories(self) -> List[Dict[str, Any]]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        cursor.execute("SELECT size_category, price, category_name FROM storage_categories")
        categories = cursor.fetchall()
        cursor.close()
        db.close()
        return categories

    def get_category_by_section(self, section_id: int) -> Dict[str, Any]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        query = """
            SELECT sc.size_category, sc.price, sc.category_name
            FROM storage_categories sc
            JOIN box_sections bs ON sc.size_category = bs.size_category
            WHERE bs.section_id = %s
        """
        cursor.execute(query, (section_id,))
        category = cursor.fetchone()
        cursor.close()
        db.close()
        return category

    def check_section_availability(self, section_id: int) -> dict:
        """Check if section is available for storage reservation"""
        # Use find_reservations to check for active reservations
        active_reservations = self.find_reservations(
            section_id=section_id,
            status=1,
            limit=1
        )

        if active_reservations:
            return {"available": False, "reason": "Section already has an active reservation"}

        # Check if section exists and has mode == "storage"
        db = get_db()
        cursor = db.cursor(dictionary=True)

        try:
            cursor.execute("""
                SELECT section_id, mode FROM box_sections
                WHERE section_id = %s
            """, (str(section_id),))
            section = cursor.fetchone()

            if not section:
                return {"available": False, "reason": "Section not found"}

            if section['mode'] != 'storage':
                return {"available": False, "reason": f"Section mode is '{section['mode']}', only 'storage' mode allowed"}

            return {"available": True, "reason": "Section is available"}

        except Exception as e:
            return {"available": False, "reason": f"Database error: {str(e)}"}
        finally:
            cursor.close()
            db.close()

    def create_reservation(self, section_id: int, price: float, size_category: int, email: str = None) -> Tuple[str, str]:
        import random
        from uuid import uuid4
        from os import getenv

        db = get_db()
        cursor = db.cursor()

        # Generate a unique 6-digit PIN
        from .pin_generator import generate_pin
        pin = generate_pin()

        if pin is None:
            cursor.close()
            db.close()
            raise Exception("Failed to generate unique PIN for reservation")

        # Generate UUID for the reservation
        reservation_uuid = str(uuid4())

        # Get box_uuid from box_sections table
        cursor.execute("SELECT box_uuid FROM box_sections WHERE section_id = %s", (str(section_id),))
        box_result = cursor.fetchone()
        box_uuid = box_result[0] if box_result else None

        # Get MAX_STORAGE_HOURS from .env to save with the reservation
        max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))  # Default to 25 if not set

        query = """
            INSERT INTO storage_reservations (uuid, email, box_uuid, section_id, price, category, status, reservation_pin, paid_status, max_hours, created_at, last_update)
            VALUES (%s, %s, %s, %s, %s, %s, 1, %s, 'paid', %s, NOW(), NOW())
        """
        try:
            cursor.execute(query, (reservation_uuid, email, box_uuid, str(section_id), price, size_category, pin, max_storage_hours))
            db.commit()
            log_timeline_event(
                event_type="create_reservaion",
                event_result="success",
                section_id=str(section_id),
                message=f"Reservation created with PIN: {pin}",
                mode="storage"
            )

            # Send storage change status to Jetveo
            storage_change_status_async(
                reservation_uuid=reservation_uuid,
                reservation_pin=pin,
                section_id=section_id,
                email=email or "",
                size_category=size_category,
                paid_price=price,
                paid_fine=0,
                action=1,
                status=1
            )

            return reservation_uuid, pin
        except mysql.connector.Error as err:
            log_timeline_event(
                event_type="create_reservation",
                event_result="failed",
                section_id=str(section_id),
                message=f"Database error",
                mode="storage"
            )
            raise err
        finally:
            cursor.close()
            db.close()

    def find_reservation_by_pin(self, pin: str = None, uuid: str = None) -> Dict[str, Any]:
        """
        Find storage reservation by PIN or UUID.

        Args:
            pin: Reservation PIN to search by
            uuid: Reservation UUID to search by

        Returns:
            Reservation record if found, None otherwise
        """
        if not pin and not uuid:
            return None

        # Use find_reservations function
        reservations = self.find_reservations(
            reservation_pin=pin,
            reservation_uuid=uuid,
            status=1,
            limit=1
        )

        if reservations:
            return reservations[0]
        else:
            # if not reservation:
            #     log_timeline_event(
            #         event_type="box_not_found",
            #         event_result="failure",
            #         message="Storage reserved Box not found",
            #         entered_pin=pin or uuid
            #     )
            return None

    def get_storage_sections(self) -> List[Dict[str, Any]]:
        """Get all storage sections with availability status"""
        db = get_db()
        cursor = db.cursor(dictionary=True)

        try:
            # Get all sections with mode = 'storage' and join with storage_categories to get category_name and price
            cursor.execute("""
                SELECT bs.section_id, bs.identification_name, bs.tempered, bs.blocked, bs.service,
                       bs.mode, bs.type, bs.size_width, bs.size_depth, bs.size_height, bs.size_category,
                       sc.category_name, sc.price
                FROM box_sections bs
                LEFT JOIN storage_categories sc ON bs.size_category = sc.size_category
                WHERE bs.visible = 1 AND bs.mode = 'storage'
                ORDER BY bs.section_id ASC
            """)
            sections = cursor.fetchall()

            # Check availability for each section
            for section in sections:
                section_id = section['section_id']
                identification_name = section['identification_name']
                section['is_available'] = self._check_section_availability_simple(cursor, section_id, identification_name)

            return sections

        except Exception:
            return []
        finally:
            cursor.close()
            db.close()

    def _check_section_availability_simple(self, cursor, section_id: int, identification_name: str = None) -> bool:
        """Check if section is available (no active storage reservations)"""
        try:
            # Sections with identification_name == "stock" are always available
            if identification_name == "stock":
                return True

            # Only check storage_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM storage_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            storage_count = cursor.fetchone()['count']

            return storage_count == 0

        except Exception as e:
            return False

    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str):
        """Handle section open for storage endpoints"""
        match endpoint_type:
            case "storage/insert":
                # No action needed for insert, as it's already handled in the flow
                return
            case "storage/pickup":
                # Get the paid amount from session for storage pickup and deactivate reservation
                from managers.session_manager import session_manager
                session = session_manager.get_session(session_id)
                paid_fee = getattr(session, 'amount', 0.0) if session else 0.0

                try:
                    # First, find the reservation to check the 15-minute rule and calculate extra charges
                    reservations = self.find_reservations(section_id=section_id, status=1)

                    if not reservations:
                        log_timeline_event(
                            event_type="reservation_status_changed",
                            event_result="failed",
                            section_id=str(section_id),
                            message=f"No active reservation found for section ID: {section_id}"
                        )
                        return False

                    reservation = reservations[0]  # Get the first active reservation
                    reservation_uuid = reservation.get('uuid')

                    # Check if reservation should be deactivated (15-minute rule)
                    if not self.should_deactivate_reservation(reservation_uuid):
                        log_timeline_event(
                            event_type="reservation_status_changed",
                            event_result="skipped",
                            section_id=str(section_id),
                            message=f"Reservation {reservation_uuid} is within 15-minute grace period, not deactivating"
                        )
                        return True  # Allow pickup but don't deactivate

                    # Calculate extra charge for storage time
                    extra_charge = self.calculate_storage_extra_charge(reservation_uuid)
                    total_paid_fee = paid_fee + extra_charge

                    # Use edit_reservations to deactivate the reservation
                    updated_reservations = await self.edit_reservations(
                        section_id=section_id,
                        status_to_set=0,  # Deactivate
                        paid_fee=total_paid_fee,  # Include extra charge
                        action=0  # purchased/completed
                    )

                    if updated_reservations:
                        log_timeline_event(
                            event_type="reservation_status_changed",
                            event_result="complete",
                            section_id=str(section_id),
                            message=f"Reservation status changed to 0 for section ID: {section_id}, base fee: {paid_fee}, extra charge: {extra_charge}, total fee: {total_paid_fee}"
                        )
                        return True
                    else:
                        log_timeline_event(
                            event_type="reservation_status_changed",
                            event_result="failed",
                            section_id=str(section_id),
                            message=f"No active reservation found for section ID: {section_id}"
                        )
                        return False

                except Exception as e:
                    log_timeline_event(
                        event_type="reservation_status_changed",
                        event_result="failed",
                        section_id=str(section_id),
                        message=f"Error deactivating reservation for section ID: {section_id} - {e}",
                    )
                    logger.error(f"Error deactivating reservation: {e}")
                    return False
            case _:
                pass  # No action needed for other endpoints


    def find_reservations(
        self,
        section_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,
        reservation_uuid: Optional[str] = None,
        status: Optional[int] = None,
        email: Optional[str] = None,
        paid_status: Optional[str] = None,
        limit: Optional[int] = None,
        order_by: str = "created_at DESC"
    ) -> List[Dict[str, Any]]:
        """
        Function to find storage reservations based on search parameters.

        Args:
            section_id: Section ID to search in
            reservation_pin: Reservation PIN to search by
            reservation_uuid: Reservation UUID to search by
            status: Status to filter by (0=inactive, 1=active)
            email: Email address to search by
            paid_status: Payment status to filter by ('paid', 'unpaid', etc.)
            limit: Maximum number of results to return
            order_by: Order by clause (default: "created_at DESC")

        Returns:
            List of storage reservation records matching the criteria
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)

        try:
            # Build WHERE clause dynamically
            where_conditions = []
            params = []

            if section_id is not None:
                where_conditions.append("section_id = %s")
                params.append(str(section_id))

            if reservation_pin is not None:
                where_conditions.append("reservation_pin = %s")
                params.append(reservation_pin)

            if reservation_uuid is not None:
                where_conditions.append("uuid = %s")
                params.append(reservation_uuid)

            if status is not None:
                where_conditions.append("status = %s")
                params.append(status)

            if email is not None:
                where_conditions.append("email = %s")
                params.append(email)

            if paid_status is not None:
                where_conditions.append("paid_status = %s")
                params.append(paid_status)

            # Build the query
            query = "SELECT * FROM storage_reservations"
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            query += f" ORDER BY {order_by}"
            if limit is not None:
                query += f" LIMIT {limit}"

            cursor.execute(query, params)
            results = cursor.fetchall()

            logger.info(f"Found {len(results)} storage reservations matching criteria")
            return results

        except mysql.connector.Error as err:
            logger.error(f"Database error finding storage reservations: {err}")
            return []
        finally:
            cursor.close()
            db.close()


    async def edit_reservations(
        self,
        # Search parameters
        section_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,
        reservation_uuid: Optional[str] = None,
        status_to_find: Optional[int] = 1,

        # Update parameters
        status_to_set: Optional[int] = None,
        paid_status: Optional[str] = None,
        paid_fee: Optional[float] = None,
        new_reservation_pin: Optional[str] = None,
        action: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Function to edit storage reservations based on search parameters.

        Args:
            # Search parameters (at least one must be provided)
            section_id: Section ID to find reservation
            reservation_pin: Reservation PIN to find reservation
            reservation_uuid: Reservation UUID to find reservation
            status_to_find: Status to search for (default: 1=active)

            # Update parameters (at least one must be provided)
            status_to_set: New status (0=inactive, 1=active)
            paid_status: New payment status ('paid', 'unpaid', etc.)
            paid_fee: New paid fee amount
            new_reservation_pin: New reservation PIN
            action: Action code for Jetveo (0=purchased, 1=inserted, 2=removed, 3=no change)

        Returns:
            List of updated reservation records if successful, empty list otherwise
        """

        # Validate that at least one search parameter is provided
        search_params = [section_id, reservation_pin, reservation_uuid]
        if not any(param is not None for param in search_params):
            logger.error("At least one search parameter must be provided")
            return []

        # Validate that at least one update parameter is provided
        update_params = [status_to_set, paid_status, paid_fee, new_reservation_pin]
        if not any(param is not None for param in update_params):
            logger.error("At least one update parameter must be provided")
            return []

        # First, find the reservations using find_reservations function to load parameters
        reservations = self.find_reservations(
            section_id=section_id,
            reservation_pin=reservation_pin,
            reservation_uuid=reservation_uuid,
            status=status_to_find
        )

        if not reservations:
            logger.warning("No reservations found matching search criteria")
            return []

        # Load parameters from the found reservations
        loaded_params = []
        for res in reservations:
            loaded_params.append({
                'reservation_uuid': res['uuid'],
                'section_id': res['section_id'],
                'current_status': res.get('status'),
                'current_paid_status': res.get('paid_status'),
                'current_paid_fee': res.get('paid_fee'),
                'email': res.get('email'),
                'price': res.get('price'),
                'category': res.get('category')
            })

        db = get_db()
        cursor = db.cursor(dictionary=True)
        updated_reservations = []

        try:
            for params in loaded_params:
                # Build UPDATE query dynamically
                update_fields = []
                update_query_params = []

                if status_to_set is not None:
                    update_fields.append("status = %s")
                    update_query_params.append(status_to_set)

                if paid_status is not None:
                    update_fields.append("paid_status = %s")
                    update_query_params.append(paid_status)

                if paid_fee is not None:
                    update_fields.append("paid_fee = %s")
                    update_query_params.append(float(paid_fee))

                if new_reservation_pin is not None:
                    update_fields.append("reservation_pin = %s")
                    update_query_params.append(new_reservation_pin)

                # Always update last_update timestamp
                from datetime import datetime
                update_fields.append("last_update = %s")
                update_query_params.append(datetime.now())

                # Execute update using the reservation UUID
                update_query_params.append(params['reservation_uuid'])
                update_query = f"UPDATE storage_reservations SET {', '.join(update_fields)} WHERE uuid = %s"
                cursor.execute(update_query, update_query_params)

                if cursor.rowcount > 0:
                    # Get updated record using the reservation UUID
                    cursor.execute("SELECT * FROM storage_reservations WHERE uuid = %s", (params['reservation_uuid'],))
                    updated_reservation = cursor.fetchone()

                    if updated_reservation:
                        updated_reservations.append(updated_reservation)
                        logger.info(f"Successfully updated storage reservation {updated_reservation['uuid']}")

            db.commit()

            # Send storage change status to Jetveo for ALL edited reservations
            for updated_reservation in updated_reservations:
                try:
                    storage_change_status_async(
                        reservation_uuid=updated_reservation['uuid'],
                        section_id=updated_reservation['section_id'],
                        price=updated_reservation.get('price'),
                        email=updated_reservation.get('email'),
                        paid_status=updated_reservation.get('paid_status'),
                        action=action if action else 0,  # 0=purchased, 1=inserted, 2=removed, 3=no change
                        status=updated_reservation.get('status')
                    )
                    logger.info(f"Sent storage_change_status for reservation {updated_reservation['uuid']}")
                except Exception as e:
                    logger.error(f"Error sending storage_change_status for reservation {updated_reservation['uuid']}: {e}")

            logger.info(f"Successfully updated {len(updated_reservations)} storage reservations")
            return updated_reservations

        except mysql.connector.Error as err:
            db.rollback()
            logger.error(f"Database error editing storage reservations: {err}")
            return []
        finally:
            cursor.close()
            db.close()



    def calculate_storage_extra_charge(self, reservation_uuid: str) -> float:
        """
        Calculate extra charge for storage reservation based on how many MAX_STORAGE_HOURS periods have passed.

        Args:
            reservation_uuid: UUID of the reservation to calculate charge for

        Returns:
            Extra charge amount (0.0 if no extra charge needed)
        """
        try:
            from datetime import datetime
            from os import getenv

            # Get configuration from environment
            max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))
            extra_charge_amount = float(getenv("EXTRA_CHARGE_AMOUNT", "63"))

            db = get_db()
            cursor = db.cursor(dictionary=True)

            # Get the reservation creation time
            query = "SELECT created_at FROM storage_reservations WHERE uuid = %s AND status = 1"
            cursor.execute(query, (reservation_uuid,))
            reservation = cursor.fetchone()

            cursor.close()
            db.close()

            if not reservation:
                logger.warning(f"Active reservation {reservation_uuid} not found")
                return 0.0

            created_at = reservation['created_at']
            now = datetime.now()

            # Calculate how long the reservation has been active
            time_diff = now - created_at
            hours_stored = time_diff.total_seconds() / 3600  # Convert to hours

            # Calculate how many MAX_STORAGE_HOURS periods have passed
            if hours_stored <= max_storage_hours:
                # No extra charge if within the first period
                extra_charge = 0.0
                logger.info(f"Reservation {reservation_uuid} stored for {hours_stored:.1f}h, no extra charge")
            else:
                # Calculate number of extra periods
                extra_periods = int((hours_stored - max_storage_hours) / max_storage_hours) + 1
                extra_charge = extra_charge_amount * extra_periods
                logger.info(f"Reservation {reservation_uuid} stored for {hours_stored:.1f}h, {extra_periods} extra periods, charge: {extra_charge}")

            return extra_charge

        except Exception as e:
            logger.error(f"Error calculating storage extra charge for {reservation_uuid}: {e}")
            return 0.0

    def should_deactivate_reservation(self, reservation_uuid: str) -> bool:
        """
        Check if a storage reservation should be deactivated based on the 15-minute rule.
        If the reservation was created less than 15 minutes ago, it should NOT be deactivated.

        Args:
            reservation_uuid: UUID of the reservation to check

        Returns:
            True if reservation should be deactivated, False if it's within the 15-minute grace period
        """
        try:
            from datetime import datetime, timedelta
            from os import getenv

            # Get the cancel time from environment (default 15 minutes)
            cancel_time_minutes = int(getenv("STORAGE_RESERVATION_CANCEL_TIME", "15"))
            grace_period = timedelta(minutes=cancel_time_minutes)
            cutoff_time = datetime.now() - grace_period

            db = get_db()
            cursor = db.cursor(dictionary=True)

            # Get the reservation creation time
            query = "SELECT created_at FROM storage_reservations WHERE uuid = %s"
            cursor.execute(query, (reservation_uuid,))
            reservation = cursor.fetchone()

            cursor.close()
            db.close()

            if not reservation:
                logger.warning(f"Reservation {reservation_uuid} not found")
                return True  # If not found, allow deactivation

            created_at = reservation['created_at']

            # If created_at is older than cutoff_time, allow deactivation
            should_deactivate = created_at <= cutoff_time

            if not should_deactivate:
                logger.info(f"Reservation {reservation_uuid} is within {cancel_time_minutes}-minute grace period, not deactivating")
            else:
                logger.info(f"Reservation {reservation_uuid} is older than {cancel_time_minutes} minutes, allowing deactivation")

            return should_deactivate

        except Exception as e:
            logger.error(f"Error checking reservation deactivation rule for {reservation_uuid}: {e}")
            return True  # If error, allow deactivation to be safe

    def get_storage_extra_charge_info(self, reservation_uuid: str) -> Dict[str, Any]:
        """
        Get detailed information about storage extra charges for a reservation.
        Useful for displaying charge information to customers.

        Args:
            reservation_uuid: UUID of the reservation

        Returns:
            Dictionary with charge information
        """
        try:
            from datetime import datetime
            from os import getenv

            # Get configuration from environment
            max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))
            extra_charge_amount = float(getenv("EXTRA_CHARGE_AMOUNT", "63"))

            db = get_db()
            cursor = db.cursor(dictionary=True)

            # Get the reservation
            query = "SELECT * FROM storage_reservations WHERE uuid = %s AND status = 1"
            cursor.execute(query, (reservation_uuid,))
            reservation = cursor.fetchone()

            cursor.close()
            db.close()

            if not reservation:
                return {
                    "found": False,
                    "error": "Reservation not found or not active"
                }

            created_at = reservation['created_at']
            now = datetime.now()

            # Calculate storage time
            time_diff = now - created_at
            hours_stored = time_diff.total_seconds() / 3600
            days_stored = hours_stored / 24

            # Calculate charges
            if hours_stored <= max_storage_hours:
                extra_periods = 0
                extra_charge = 0.0
            else:
                extra_periods = int((hours_stored - max_storage_hours) / max_storage_hours) + 1
                extra_charge = extra_charge_amount * extra_periods

            return {
                "found": True,
                "reservation_uuid": reservation_uuid,
                "section_id": reservation.get('section_id'),
                "created_at": created_at.isoformat(),
                "hours_stored": round(hours_stored, 1),
                "days_stored": round(days_stored, 1),
                "max_storage_hours": max_storage_hours,
                "extra_charge_per_period": extra_charge_amount,
                "extra_periods": extra_periods,
                "extra_charge_total": extra_charge,
                "base_price": reservation.get('price', 0.0),
                "total_with_extra": (reservation.get('price', 0.0) or 0.0) + extra_charge
            }

        except Exception as e:
            logger.error(f"Error getting storage extra charge info for {reservation_uuid}: {e}")
            return {
                "found": False,
                "error": str(e)
            }


storage_repository = StorageRepository()
