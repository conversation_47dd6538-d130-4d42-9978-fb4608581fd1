"""
WebSocket handler for storage operations.
Handles storage pickup using pickup_process directly and storage insertion using select_sections from process_manager.
"""

import json
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from infrastructure.repositories.storage_repository import StorageRepository
from managers.logger_manager import logger


async def handle_storage_pickup_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage pickup operations.
    Uses pickup_process directly as defined in screen_communication.md

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage pickup WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage pickup WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        requires_payment = getattr(session, 'amount', 0) > 0
        payment_completed = getattr(session, 'payment_completed', False)

        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler to route WebSocket messages to pickup_process
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to pickup_process
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage pickup WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Start pickup_process directly from universal process_manager
        # Skip payment if already completed
        effective_requires_payment = requires_payment and not payment_completed
        if requires_payment and not payment_completed:
            session_manager.update_session(session_id, current_step="payment")
        elif not requires_payment or payment_completed:
            session_manager.update_session(session_id, current_step="hardware")

        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=[section_id],
            session_id=session_id,
            message_queue=message_queue,
            requires_payment=effective_requires_payment
        )

        # Mark as completed if successful
        if success:
            session_manager.update_session(session_id, current_step="completed", payment_completed=True)

        # Cancel message handler
        message_task.cancel()

        # Complete storage pickup operation
        # if success and successful_sections and reservation_id:
        #     repo = StorageRepository()
        #     repo.deactivate_reservation(reservation_id)
        #     logger.info(f"Deactivated storage reservation {reservation_id} after pickup")

        logger.info(f"Storage pickup completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage pickup WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Storage pickup WebSocket handler ended for session: {session_id}")


async def handle_storage_insert_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage insertion operations.
    Uses select_sections function from process_manager for door opening.

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage insert WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage insert WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for communication with select_sections
        message_queue = asyncio.Queue()


        # Message handler to route WebSocket messages to select_sections
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to select_sections
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage insert WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Get session data for payment requirements
        requires_payment = getattr(session, 'amount', 0) > 0
        current_step = getattr(session, 'current_step', None)
        payment_completed = getattr(session, 'payment_completed', False)

        # PAYMENT STEP - Use universal pickup_process for payment handling
        if requires_payment and not payment_completed:
            logger.info("Payment required, using universal pickup_process for payment")

            # Update session state
            session_manager.update_session(session_id, current_step="payment")

            # Use universal pickup_process just for payment (with empty sections list)
            from managers.payment_manager import payment_process_with_callbacks
            payment_success = await payment_process_with_callbacks(
                session_id=session_id,
                amount=getattr(session, 'amount', 0),
                message_queue=message_queue
            )

            if not payment_success:
                logger.info("Payment failed or cancelled - ending insertion process")
                return

            # Mark payment as completed
            session_manager.update_session(session_id, payment_completed=True, current_step="hardware")
            logger.info("Payment completed successfully - proceeding to section selection")
        elif requires_payment and payment_completed:
            logger.info("Payment already completed, skipping payment step")
            session_manager.update_session(session_id, current_step="hardware")

        # CREATE RESERVATION
        from infrastructure.repositories.storage_repository import storage_repository as repo
        reservation_uuid, pickup_pin = repo.create_reservation(
            section_id=section_id,
            price=getattr(session, 'amount', 0),
            size_category=getattr(session, 'size_category', 0),
            email=getattr(session, 'email', None)
            )
        
        logger.info(f"Created storage reservation {reservation_uuid} for section {section_id} with pickup pin {pickup_pin}")

        # Calculate expiration time in format 22.09.2025 10:00:00
        # expiration_time = current_time + MAX_STORAGE_HOURS - 1
        from datetime import datetime, timedelta
        from os import getenv

        max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))
        current_time = datetime.now()
        expiration_time_dt = current_time + timedelta(hours=max_storage_hours - 1)
        expiration_time = expiration_time_dt.strftime("%d.%m.%Y %H:%M:%S")

        await ws_manager.send(session_id, {
            "type": "start_pin_screen",
            "pickup_pin": pickup_pin,
            "message": "reservation created, waiting for pin confirmation",
            "expiration_time": expiration_time,
            "expiration_time_hours": max_storage_hours - 1
        })

        # PIN CONFIRMATION
        pin_confirmed_state = 0
        while pin_confirmed_state != 2:
            try:
                message = await message_queue.get()
                if message.get("type") == "pin_screen_ready":
                    await ws_manager.send(session_id, {
                        "type": "stop_pin_screen",
                        "pickup_pin": pickup_pin,
                        "message": "waiting for pin_screen_stop command",
                        "expiration_time": expiration_time,
                        "expiration_time_hours": max_storage_hours - 1
                    })
                    pin_confirmed_state = 1
                
                if message.get("type") == "pin_screen_stop" and pin_confirmed_state == 1:
                    pin_confirmed_state = 2
            except Exception as e:
                logger.error(f"Error waiting for pin confirmation: {e}")
                break

        # OPEN SECTION
        from managers.process_manager import select_sections
        success, selected_sections = await select_sections(
            reserved_sections=[section_id],  # Only one section is reserved for storage insertion
            available_sections=[section_id],  # Only one section is available for storage insertion
            session_id=session_id,
            message_queue=message_queue,
        )

        # Cancel message handler
        message_task.cancel()

        # Complete storage insertion operation
        if success:
            session_manager.update_session(session_id, current_step="completed")
            logger.info(f"Storage insertion completed successfully for section {section_id}")

        logger.info(f"Storage insertion completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage insert WebSocket handler: {e}")
    finally:
        # Clean up WebSocket connection
        ws_manager.disconnect(session_id)
        logger.info(f"Storage insert WebSocket handler ended for session: {session_id}")




async def handle_storage_websocket(websocket: WebSocket, session_id: str):
    """
    Main WebSocket handler for storage operations.
    Routes to appropriate handler based on session operation type.
    """
    from managers.session_manager import session_manager

    session = session_manager.get_session(session_id)
    if not session:
        logger.error(f"No session found for {session_id}")
        await websocket.close(code=1000, reason="Session not found")
        return

    operation = getattr(session, 'operation', None)

    if operation == "storage_pickup":
        await handle_storage_pickup_websocket(websocket, session_id)
    elif operation == "storage_insert":
        await handle_storage_insert_websocket(websocket, session_id)
    else:
        # No other storage operations supported
        logger.error(f"Unknown storage operation: {operation}")
        await websocket.close(code=1000, reason="Unknown operation")


