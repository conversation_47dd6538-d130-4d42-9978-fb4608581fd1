"""
Order API Router.
Handles employment order management endpoints.
"""

from managers.logger_manager import logger
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from order.models import (
    EmploymentPickupExpiredRequest, EmploymentPickupExpiredResponse,
    EmploymentPickupRequest, EmploymentPickupResponse,
    EmploymentDeliverRequest, EmploymentDeliverResponse,
    OrderDeliverRequest, OrderDeliverResponse,
    CheckAfterDeliveryRequest, CheckAfterDeliveryResponse,
    EmploymentSendRequest, EmploymentSendResponse,
    CustomerPickupRequest, CustomerPickupResponse,
    CustomerReclaimRequest, CustomerReclaimResponse,
    CustomerSendRequest, CustomerSendResponse,
    CreateReservationRequest, CreateReservationResponse,
    ListOrdersResponse
)
from domains.order.service import order_service
from managers.timeline_logger import log_timeline_event

router = APIRouter()

@router.post("/operator/pickup-expired", response_model=EmploymentPickupExpiredResponse)
async def pickup_expired_orders(request: EmploymentPickupExpiredRequest):
    """
    Request to pickup expired orders.
    Creates a WebSocket session and starts pickup loop if expired orders are found.
    """
    try:
        result = await order_service.pickup_expired_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup_expired"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupExpiredResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_expired_orders: {e}")
        log_timeline_event(
            event_type="pickup_expired",
            event_result="failed",
            operator_id=str(request.operator_id),
            message=f"Error in pickup_expired_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupExpiredResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/operator/pickup", response_model=EmploymentPickupResponse)
async def pickup_employee_orders(request: EmploymentPickupRequest):
    """
    Function for operator to pickup orders from employees.
    Creates a WebSocket session and starts pickup loop if employee orders are found.
    """
    try:
        result = await order_service.operator_pickup_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_employee_orders: {e}")
        log_timeline_event(
            event_type="pickup",
            event_result="failed",
            operator_id=str(request.operator_id),
            message=f"Error in pickup_employee_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/operator/deliver", response_model=EmploymentDeliverResponse)
async def deliver_to_employee(request: EmploymentDeliverRequest):
    """
    Function to deliver orders to employees. This function is for operator.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.deliver_to_employee(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "deliver_employee"
        
        return EmploymentDeliverResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in deliver_to_employee: {e}")
        log_timeline_event(
            event_type="deliver_employee",
            event_result="failed",
            message=f"Error in deliver_to_employee: {str(e)}",
            mode="order"
        )
        return EmploymentDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_ids=None
        )

@router.post("/operator/deliver", response_model=OrderDeliverResponse)
async def operator_deliver_orders(request: OrderDeliverRequest):
    """
    Function for operator to deliver orders with status 7 or 8.
    Creates WebSocket session if there are orders to deliver.
    """
    try:
        result = await order_service.operator_deliver()

        return OrderDeliverResponse(**result)

    except Exception as e:
        logger.error(f"Error in operator_deliver_orders: {e}")
        log_timeline_event(
            event_type="operator_deliver",
            event_result="failed",
            message=f"Error in operator_deliver_orders: {str(e)}",
            mode="order"
        )
        return OrderDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            total_reservations=0
        )

@router.post("/operator/check-after-delivery", response_model=CheckAfterDeliveryResponse)
async def check_after_delivery(request: CheckAfterDeliveryRequest):
    """
    Check if all packages for each order were delivered.
    Complete orders where all packages have status 3, and start pickup for incomplete orders.
    """
    try:
        result = await order_service.check_after_delivery()

        return CheckAfterDeliveryResponse(**result)

    except Exception as e:
        logger.error(f"Error in check_after_delivery: {e}")
        log_timeline_event(
            event_type="check_after_delivery",
            event_result="failed",
            message=f"Error in check_after_delivery: {str(e)}",
            mode="order"
        )
        return CheckAfterDeliveryResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0,
            needs_pickup=False,
            completed_orders=0
        )

@router.post("/employment/customer/send", response_model=EmploymentSendResponse)
async def employee_send_order(request: EmploymentSendRequest):
    """
    Function for employee to send order.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.employee_send_order(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"] and result["valid"]:

            log_timeline_event(
                event_type="employee_send",
                event_result="started",
                phone_number=request.phone_number,
                message="Employee send order started",
                mode="order"
            )

            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "employee_send"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "phone_number": request.phone_number,
                    "reserved_section_id": result.get("section_id")
                })
        
        elif not result["valid"]:
            log_timeline_event(
                event_type="employee_send",
                event_result="phone_number_not_found",
                phone_number=request.phone_number,
                message="Invalid phone number",
                mode="order"
            )

        return EmploymentSendResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in employee_send_order: {e}")
        log_timeline_event(
            event_type="employee_send",
            event_result="failed",
            phone_number=request.phone_number,
            message=f"Error in employee_send_order: {str(e)}",
            mode="order"
        )
        return EmploymentSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/pickup", response_model=CustomerPickupResponse)
async def customer_pickup_order(request: CustomerPickupRequest):
    """
    Function for customer to pickup order using PIN.
    Similar to product pickup - creates WebSocket session and waits for hardware_screen_ready.
    """
    try:
        result = await order_service.customer_pickup_order(request.pickup_pin)
        
        # If order is found, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_pickup"
                session.section_id = result["section_id"]  # This field exists in SessionData
                if not session.user_data:
                    session.user_data = {}

            log_timeline_event(
                event_type="pin_entered",
                event_result="order_found",
                entered_pin=request.pickup_pin,
                message="Customer pickup started",
                mode="order"
            )
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="order_not_found",
                entered_pin=request.pickup_pin,
                message="Invalid pickup PIN",
                mode="order"
            )
        
        return CustomerPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in customer_pickup_order: {e}")
        log_timeline_event(
            event_type="pin_entered",
            event_result="failed",
            entered_pin=request.pickup_pin,
            message=f"Error in customer_pickup_order: {str(e)}",
            mode="order"
        )
        return CustomerPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_id=None,
            requires_payment=False,
            amount=0.0
        )

@router.post("/employment/customer/reclaim", response_model=CustomerReclaimResponse)
async def customer_reclaim_order(request: CustomerReclaimRequest):
    """
    Function for customer to reclaim order using reclamation PIN.
    Validates reclamation PIN and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.customer_reclaim_order(request.reclamation_pin)

        # If reclamation PIN is valid, start the flow
        if result["success"] and result["valid"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_reclaim"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "phone_number": result.get("phone_number"),
                    "reserved_section_id": result.get("section_id")
                })

            log_timeline_event(
                event_type="reclamation_pin_entered",
                event_result="valid_pin",
                entered_pin=request.reclamation_pin,
                message="Reclamation PIN is valid",
                mode="order"
            )
            
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="invalid_pin",
                entered_pin=request.reclamation_pin,
                message="Invalid reclamation PIN",
                mode="order"
            )

        return CustomerReclaimResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_reclaim_order: {e}")
        log_timeline_event(
            event_type="reclamation_pin_entered",
            event_result="failed",
            entered_pin=request.reclamation_pin,
            message=f"Error in customer_reclaim_order: {str(e)}",
            mode="order"
        )
        return CustomerReclaimResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/send", response_model=CustomerSendResponse)
async def customer_send_order(request: CustomerSendRequest):
    """
    Function for customer to send order to operator using reservation PIN.
    Checks if reservation exists with status=8 (ready for insert) and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.customer_send_order(request.reservation_pin)

        # If order is found, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_send"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "reservation_pin": request.reservation_pin,
                    "reserved_section_id": result.get("section_id")
                })

            log_timeline_event(
                event_type="customer_send_started",
                event_result="success",
                entered_pin=str(request.reservation_pin),
                message="Customer send order started",
                mode="order"
            )

        return CustomerSendResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_send_order: {e}")
        log_timeline_event(
            event_type="customer_send",
            event_result="failed",
            message=f"Error in customer_send_order: {str(e)}",
            mode="order"
        )
        return CustomerSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            message=f"Internal server error: {str(e)}"
        )


@router.post("/create-reservation", response_model=CreateReservationResponse)
async def create_reservation(request: CreateReservationRequest):
    """
    Create order reservations for selected sections.
    This endpoint is called after select_sections() completes successfully.

    Request body:
    {
        "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",
        "type": "successfully_inserted", // or "insert_unsuccessful" if customer did not insert the order
        "sections": [1,2,3]
    }

    Response body:
    {
        "success": true,
        "message": "Created 3 reservations",
        "sections": [1,2,3]
    }
    """
    try:
        if request.type == "successfully_inserted":
            logger.info(f"Creating reservations for successfully inserted sections: {request.sections}")

            from managers.session_manager import session_manager
            from infrastructure.repositories.order_repository import order_repository
            from infrastructure.repositories.pin_generator import generate_pin
            from infrastructure.repositories.section_repository import section_repository

            # Get session data
            session = session_manager.get_session(request.session_id)
            if not session:
                return CreateReservationResponse(
                    success=False,
                    message="Session not found",
                )

            # Get session details
            reservations_info = getattr(session, 'reservations_info', None)
            endpoint_type = getattr(session, 'endpoint_type', 'unknown')
            phone_number = getattr(session, 'phone_number', None)     # for MAGNA
            selected_sections = request.sections
            used_sections = []

            result = None

            # Create reservations based on endpoint type
            if endpoint_type == "order/employment/operator/deliver":
                new_pickup_pin = generate_pin()
                for section in selected_sections:
                    result = await order_repository.create_reservation(phone_number=phone_number, section_id=section, status=1, pickup_pin=new_pickup_pin)
                    used_sections.append(section)

            elif endpoint_type == "order/employment/customer/send" and phone_number:
                for section in request.sections:
                    result = await order_repository.create_reservation(phone_number=phone_number, section_id=section, status=8)
                    used_sections.append(section)

            elif endpoint_type == "order/employment/customer/reclaim" and phone_number:
                for section in request.sections:
                    result = await order_repository.create_reservation(phone_number=phone_number, section_id=section, status=8)
                    used_sections.append(section)

            elif endpoint_type == "order/customer/send":
                # TODO: implement this later
                pass
            else:
                logger.info(f"Unknown endpoint_type '{endpoint_type}' or missing required data for reservation creation")

            return CreateReservationResponse(
                success=True if result else False,
                message=f"Created {len(used_sections)} created successfully" if result else "Failed to create reservation",
            )
        
        elif request.type == "failed_insert":
            logger.info(f"Customer did not insert the order")
            
            log_timeline_event(
                event_type="insert_order",
                event_result="failed",
                message=f"Customer did not insert the order",
                mode="order"
            )
            
            return CreateReservationResponse(
                success=True,
                message=f"Customer did not insert the order",
            )

    except Exception as e:
        logger.error(f"Error in create_reservation: {e}")
        return CreateReservationResponse(
            success=False,
            message=f"Internal server error: {str(e)}",
        )




# # Create reservation - payload:
# {
#     'SerialNumber': '123456789-1',
#     'ReservationUuid': '33dc1c21-55a5-4112-b952-ed9c48164b7c',
#     'Timestamp': '2025-09-15T08:43:16.196569Z',
#     'Action': 1,
#     'Status': 1,
#     'SectionId': 2,
#     'Price': 123.0
# }
# # Response: {'success': True, 'message': 'Success'}



# # Purchase - payload:
# {
#     'SerialNumber': '123456789-1',
#     'ReservationUuid': '33dc1c21-55a5-4112-b952-ed9c48164b7c',
#     'Timestamp': '2025-09-15T08:44:21.975111Z',
#     'Action': 0,
#     'Status': 0,
#     'SectionId': 2,
#     'Price': 123.0
# }
# # Response: {'success': True, 'message': 'Success, warnings: [Products differ: { Ean = , Price = 123 } vs { Ean = , Price = 123.0 }]'}

@router.get("/list-orders", response_model=ListOrdersResponse)
async def list_orders():
    """
    Get list of all orders with status 7, 8, or 3.
    Returns orders grouped by insert_pin with their packages.

    Response format:
    [
        {
            "insert_pin": "123456",
            "packages": [
                {
                    "package_pin": "123456",
                    "section_id": 1,
                    "status": 1
                }
            ]
        }
    ]
    """
    try:
        orders = order_service.get_orders_list()
        return ListOrdersResponse(orders=orders)

    except Exception as e:
        logger.error(f"Error in list_orders: {e}")
        log_timeline_event(
            event_type="list_orders",
            event_result="failed",
            message=f"Error in list_orders: {str(e)}",
            mode="order"
        )
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

