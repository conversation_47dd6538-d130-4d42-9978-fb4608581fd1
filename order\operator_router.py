"""
Order Operator API Router.
Handles operator-specific order management endpoints.
"""

from managers.logger_manager import logger
from fastapi import APIRouter
from typing import Dict, Any

from order.models import (
    EmploymentPickupExpiredRequest, EmploymentPickupExpiredResponse,
    EmploymentPickupRequest, EmploymentPickupResponse,
    EmploymentDeliverRequest, EmploymentDeliverResponse,
    OrderDeliverRequest, OrderDeliverResponse,
    CheckAfterDeliveryRequest, CheckAfterDeliveryResponse,
)
from domains.order.service import order_service
from managers.timeline_logger import log_timeline_event

router = APIRouter()

@router.post("/pickup-expired", response_model=EmploymentPickupExpiredResponse)
async def pickup_expired_orders(request: EmploymentPickupExpiredRequest):
    """
    Request to pickup expired orders.
    Creates a WebSocket session and starts pickup loop if expired orders are found.
    """
    try:
        result = await order_service.pickup_expired_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup_expired"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupExpiredResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_expired_orders: {e}")
        log_timeline_event(
            event_type="pickup_expired",
            event_result="failed",
            operator_id=str(request.operator_id),
            message=f"Error in pickup_expired_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupExpiredResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/pickup", response_model=EmploymentPickupResponse)
async def pickup_employee_orders(request: EmploymentPickupRequest):
    """
    Function for operator to pickup orders from employees.
    Creates a WebSocket session and starts pickup loop if employee orders are found.
    """
    try:
        result = await order_service.operator_pickup_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_employee_orders: {e}")
        log_timeline_event(
            event_type="pickup",
            event_result="failed",
            operator_id=str(request.operator_id),
            message=f"Error in pickup_employee_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/deliver", response_model=OrderDeliverResponse)
async def operator_deliver_orders(request: OrderDeliverRequest):
    """
    Function for operator to deliver orders with status 7 or 8.
    Creates WebSocket session if there are orders to deliver.
    """
    try:
        result = await order_service.operator_deliver()

        return OrderDeliverResponse(**result)

    except Exception as e:
        logger.error(f"Error in operator_deliver_orders: {e}")
        log_timeline_event(
            event_type="operator_deliver",
            event_result="failed",
            message=f"Error in operator_deliver_orders: {str(e)}",
            mode="order"
        )
        return OrderDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            total_reservations=0
        )

@router.post("/check-after-delivery", response_model=CheckAfterDeliveryResponse)
async def check_after_delivery(request: CheckAfterDeliveryRequest):
    """
    Check if all packages for each order were delivered.
    Complete orders where all packages have status 3, and start pickup for incomplete orders.
    """
    try:
        result = await order_service.check_after_delivery()

        return CheckAfterDeliveryResponse(**result)

    except Exception as e:
        logger.error(f"Error in check_after_delivery: {e}")
        log_timeline_event(
            event_type="check_after_delivery",
            event_result="failed",
            message=f"Error in check_after_delivery: {str(e)}",
            mode="order"
        )
        return CheckAfterDeliveryResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0,
            needs_pickup=False,
            completed_orders=0
        )
