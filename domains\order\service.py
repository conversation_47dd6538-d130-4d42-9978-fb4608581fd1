"""
Order service for employment order management.
Contains business logic for order operations.
"""

from typing import Dict, Any, List
from uuid import uuid4

from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.external_apis.jetveo_client import jetveo_client
from managers.session_manager import session_manager, SessionType
from managers.ws_manager import ws_manager
from managers.logger_manager import logger
from managers.timeline_logger import log_timeline_event


class OrderService:
    """Service for order operations"""
    
    def __init__(self):
        self.repo = OrderRepository()
    
    async def pickup_expired_orders(self, operator_id: int) -> Dict[str, Any]:
        """
        Initialize pickup process for expired orders.
        """
        try:
            # Get all sections with expired orders
            sections = self.repo.get_expired_order_sections()
            total_sections = len(sections)
            
            if total_sections == 0:
                return {
                    "session_id": None,
                    "success": True,
                    "message": "No expired orders found",
                    "sections": [],
                    "total_sections": 0
                }
            
            # Create session fore pickup process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="pickup_expired",
                operator_id=str(operator_id),
                sections=sections,
                endpoint_type="order/operator/pickup-expired"
            )
            
            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "sections": [],
                    "total_sections": 0
                }
            
            return {
                "session_id": session_id,
                "success": True,
                "message": "Expired orders found",
                "sections": sections,
                "total_sections": total_sections
            }
            
        except Exception as e:
            logger.error(f"Error in pickup_expired_orders: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "sections": [],
                "total_sections": 0
            }
    
    async def operator_pickup_orders(self, operator_id: int) -> Dict[str, Any]:
        """
        Initialize pickup process for employee orders.
        """
        try:
            # Get all sections with employee orders
            reservations = self.repo.find_reservations(status=8)        #TODO: zjistit jaky status tady ma byt
            sections = list(set([r['section_id'] for r in reservations]))
            total_sections = len(sections)
            
            if total_sections == 0:
                return {
                    "session_id": None,
                    "success": True,
                    "message": "No employee orders found",
                    "sections": [],
                    "total_sections": 0
                }
            
            # Create session for pickup process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="operator_pickup",
                operator_id=str(operator_id),
                sections=sections,
                endpoint_type="order/operator/pickup"
            )
            
            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "sections": [],
                    "total_sections": 0
                }
            
            return {
                "session_id": session_id,
                "success": True,
                "message": "Employee orders found",
                "sections": sections,
                "total_sections": total_sections
            }
            
        except Exception as e:
            logger.error(f"Error in pickup_employee_orders: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "sections": [],
                "total_sections": 0
            }
    
    async def deliver_to_employee(self, phone_number: str) -> Dict[str, Any]:
        """
        Initialize delivery process to employee.
        """
        try:
            # Check phone number validity with jetveo server
            validation_result = await jetveo_client.check_employment_deliver(phone_number)
            
            if not validation_result or not validation_result.get('valid', False):
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Invalid phone number",
                    "section_ids": None
                }
            
            # Get reserved section_ids from jetveo response (if any)
            reserved_section_ids = validation_result.get('section_ids')

            # Create session for delivery process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="deliver_employee",
                phone_number=phone_number,
                deliver_checked_from_server=validation_result.get('valid', False),
                user_data={
                    "phone_number": phone_number,
                    "reserved_section_ids": reserved_section_ids or []
                },
                endpoint_type="order/employment/courier/deliver"
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "section_ids": reserved_section_ids
                }

            return {
                "session_id": session_id,
                "success": True,
                "message": "Phone number validated, ready for delivery",
                "section_ids": reserved_section_ids
            }
            
        except Exception as e:
            logger.error(f"Error in deliver_to_employee: {e}")
            return {
                "success": False,
                "session_id": None,
                "message": f"Internal error: {str(e)}",
                "section_ids": None
            }



    async def operator_deliver(self) -> Dict[str, Any]:
        """
        Initialize operator delivery process.
        Creates WebSocket session if there are orders with status 7 or 8 to deliver.
        """
        try:
            # Get all reservations with status 7 or 8 (orders to deliver)
            all_reservations = await self.repo.find_reservations(statuses=[7, 8])

            if not all_reservations:
                return {
                    "success": True,
                    "session_id": None,
                    "message": "No orders to deliver",
                    "total_reservations": 0
                }

            # Create session for operator delivery process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="operator_deliver",
                endpoint_type="order/operator/deliver"
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "total_reservations": len(all_reservations)
                }

            return {
                "session_id": session_id,
                "success": True,
                "message": f"Found {len(all_reservations)} orders to deliver",
                "total_reservations": len(all_reservations)
            }

        except Exception as e:
            logger.error(f"Error in operator_deliver: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "total_reservations": 0
            }


    async def employee_send_order(self, phone_number: str) -> Dict[str, Any]:
        """
        Initialize employee send order process.
        """
        try:
            # Check phone number validity with jetveo server
            validation_result = await jetveo_client.check_employment_send(phone_number)

            if not validation_result or not validation_result.get('valid', False):
                return {
                    "success": False,
                    "valid": False,
                    "message": "Invalid phone number"
                }

            # Get reserved section_id from jetveo response (if any)
            reserved_section_id = validation_result.get('section_id')

            # Create session for send process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="employee_send",
                phone_number=phone_number,
                reserved_sections = validation_result.get('sections', []),
                endpoint_type="order/employment/customer/send"
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": reserved_section_id,
                    "valid": True,
                    "message": "Failed to create session"
                }

            return {
                "session_id": session_id,
                "success": True,
                "section_id": reserved_section_id,  # Return reserved section from jetveo
                "valid": True,
                "message": "Employee notified successfully"
            }

        except Exception as e:
            logger.error(f"Error in employee_send_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "section_id": None,
                "valid": False,
                "message": f"Internal error: {str(e)}"
            }
    
    async def customer_pickup_order(self, pickup_pin: str) -> Dict[str, Any]:
        """
        Initialize customer pickup process.
        """
        try:
            # Find reservation by pickup PIN
            reservations = self.repo.find_reservations(pickup_pin=pickup_pin, status=1)
            
            if not reservations:
                return {
                    "success": False,
                    "session_id": None,
                    "message": f"Invalid pickup PIN: {pickup_pin}",
                    "sections": [],
                    "totall_sections": 0,
                    "requires_payment": False,
                    "amount": 0.0
                }
            
            # Create session for pickup process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="customer_pickup",
                sections=[int(r['section_id']) for r in reservations],
                endpoint_type="order/customer/pickup",
                pickup_pin=pickup_pin
            )
            
            if not session:
                return {
                    "success": False,
                    "session_id": None,
                    "message": "Failed to create session",
                }
            
            return {
                "success": True,
                "session_id": session_id,
                "message": "Order found, ready for pickup",
                "sections": [int(r['section_id']) for r in reservations],
                "total_sections": len(reservations),
                "amount": 0.0
            }
            
        except Exception as e:
            logger.error(f"Error in customer_pickup_order: {e}")
            return {
                "success": False,
                "session_id": None,
                "message": f"Internal error: {str(e)}",
            }

    async def customer_reclaim_order(self, reclamation_pin: str) -> Dict[str, Any]:
        """
        Initialize customer reclaim order process.
        """
        try:
            # Check reclamation PIN validity with jetveo server
            validation_result = await jetveo_client.check_employment_reclaim(reclamation_pin)

            if not validation_result or not validation_result.get('valid', False):
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": None,
                    "valid": False,
                    "message": "Invalid reclamation PIN"
                }

            # Get phone number and reserved section_id from jetveo response
            phone_number = validation_result.get('phone_number')
            reserved_section_id = validation_result.get('section_id')

            # Create session for reclaim process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="customer_reclaim",
                phone_number=phone_number,
                user_data={
                    "phone_number": phone_number,
                    "reserved_section_id": reserved_section_id,
                    "reserved_section_ids": [reserved_section_id] if reserved_section_id else []
                },
                endpoint_type="order/employment/customer/reclaim",
                insert_pin=reclamation_pin  # Store the actual reclaim PIN entered by user as insert_pin
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": reserved_section_id,
                    "valid": True,
                    "message": "Failed to create session"
                }

            return {
                "session_id": session_id,
                "success": True,
                "section_id": reserved_section_id,  # Return reserved section from jetveo
                "valid": True,
                "phone_number": phone_number,  # Include phone number for flow config
                "message": "Employee notified successfully"
            }

        except Exception as e:
            logger.error(f"Error in customer_reclaim_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "section_id": None,
                "valid": False,
                "message": f"Internal error: {str(e)}"
            }

    async def customer_send_order(self, reservation_pin: str) -> Dict[str, Any]:
        """
        Initialize customer send order process.
        Check if reservation exists with status=8 (ready for insert).
        """
        try:
            # Find reservation with status=8 (ready for insert)
            result = self.repo.find_reservation_by_pin_and_status(reservation_pin, 8)

            if not result["success"]:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": None,
                    "message": "Order not found or not ready for pickup"
                }

            reservation = result["reservation"]
            reserved_section_id = int(reservation["section_id"]) if reservation["section_id"] else None

            # Create session for customer send process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="customer_send",
                reservation_id=reservation["id"],
                reservation_pin=reservation_pin,
                user_data={
                    "reservation_pin": reservation_pin,
                    "reserved_section_id": reserved_section_id,
                    "reserved_section_ids": [reserved_section_id] if reserved_section_id else []
                },
                endpoint_type="order/customer_send",
                pin=reservation_pin  # Store the entered reservation PIN for timeline logging
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": reserved_section_id,
                    "message": "Failed to create session"
                }

            return {
                "session_id": session_id,
                "success": True,
                "section_id": reserved_section_id,
                "message": "Order found, ready for pickup"
            }

        except Exception as e:
            logger.error(f"Error in customer_send_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "section_id": None,
                "message": f"Internal error: {str(e)}"
            }

    def get_orders_list(self) -> List[Dict[str, Any]]:
        """
        Get list of all orders with status 7, 8, or 3.
        Returns orders grouped by insert_pin with their packages.
        """
        try:
            return self.repo.get_orders_list()
        except Exception as e:
            logger.error(f"Error in get_orders_list: {e}")
            return []

    async def check_after_delivery(self) -> Dict[str, Any]:
        """
        Check if all packages for each order were delivered and handle completion/pickup.
        """
        try:
            result = await self.repo.check_after_delivery()

            if result["success"] and result["needs_pickup"]:
                # Create session for pickup process of incomplete orders
                session_id = str(uuid4())
                session = session_manager.create_session(
                    session_id=session_id,
                    session_type=SessionType.ORDER_FLOW,
                    operation="check_after_delivery_pickup",
                    sections=result["sections"],
                    endpoint_type="order/operator/check-after-delivery"
                )

                if session:
                    result["session_id"] = session_id
                else:
                    result["success"] = False
                    result["message"] = "Failed to create pickup session"
                    result["session_id"] = None
            else:
                result["session_id"] = None

            return result

        except Exception as e:
            logger.error(f"Error in check_after_delivery: {e}")
            return {
                "success": False,
                "message": f"Internal error: {str(e)}",
                "sections": [],
                "total_sections": 0,
                "needs_pickup": False,
                "session_id": None
            }

# Global service instance
order_service = OrderService()
