#!/usr/bin/env python3
"""
Test script for order module functionality.
Empties order_reservations table and inserts test records with various scenarios.
"""

import mysql.connector
from datetime import datetime, timedelta
from uuid import uuid4
import random
import string
from os import getenv
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection():
    """Get database connection using environment variables."""
    return mysql.connector.connect(
        host=getenv("DB_HOST", "localhost"),
        user=getenv("DB_USER", "root"),
        password=getenv("DB_PASSWORD", ""),
        database=getenv("DB_NAME", "test_db"),
        charset='utf8mb4',
        collation='utf8mb4_unicode_ci'
    )

def generate_pin(length=6):
    """Generate random PIN."""
    return ''.join(random.choices(string.digits, k=length))

def clear_order_reservations():
    """Empty the order_reservations table."""
    db = get_db_connection()
    cursor = db.cursor()
    
    try:
        cursor.execute("DELETE FROM order_reservations")
        db.commit()
        print("✓ Cleared order_reservations table")
    except Exception as e:
        print(f"✗ Error clearing table: {e}")
        db.rollback()
    finally:
        cursor.close()
        db.close()

def insert_test_records():
    """Insert test records for order module functionality testing."""
    db = get_db_connection()
    cursor = db.cursor()
    
    # Base timestamp
    base_time = datetime.now()
    
    test_records = [
        # Complete Order 1 - All packages delivered (status 3) - should be completed to status 2
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-111111-1',
            'insert_pin': '111111',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 1,
            'status': 3,  # Delivered
            'tempered': 0,
            'size_category': 1,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=2)
        },
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-111111-2',
            'insert_pin': '111111',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 2,
            'status': 3,  # Delivered
            'tempered': 1,
            'size_category': 2,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=2)
        },
        
        # Incomplete Order 2 - Mixed statuses (should need pickup for status 3)
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-222222-1',
            'insert_pin': '222222',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 3,
            'status': 3,  # Delivered - needs pickup
            'tempered': 0,
            'size_category': 1,
            'reserved_section_id': 5,  # Has reserved section
            'sectionc_can_change': 0,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=1)
        },
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-222222-2',
            'insert_pin': '222222',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 4,
            'status': 7,  # Ready for delivery
            'tempered': 1,
            'size_category': 3,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=1)
        },
        
        # Order 3 - Ready for delivery (status 7, 8)
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-333333-1',
            'insert_pin': '333333',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 5,
            'status': 7,  # Ready for delivery
            'tempered': 0,
            'size_category': 2,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(minutes=30)
        },
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-333333-2',
            'insert_pin': '333333',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 6,
            'status': 8,  # Ready for delivery
            'tempered': 1,
            'size_category': 1,
            'reserved_section_id': 10,  # Has reserved section
            'sectionc_can_change': 0,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(minutes=30)
        },
        
        # Order 4 - Single package order, delivered (should be completed)
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-444444-1',
            'insert_pin': '444444',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 7,
            'status': 3,  # Delivered
            'tempered': 0,
            'size_category': 3,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(minutes=45)
        },
        
        # Order 5 - Large order with mixed statuses
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-555555-1',
            'insert_pin': '555555',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 8,
            'status': 3,  # Delivered - needs pickup
            'tempered': 1,
            'size_category': 2,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=3)
        },
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-555555-2',
            'insert_pin': '555555',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 9,
            'status': 8,  # Ready for delivery
            'tempered': 0,
            'size_category': 1,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=3)
        },
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-555555-3',
            'insert_pin': '555555',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 10,
            'status': 7,  # Ready for delivery
            'tempered': 1,
            'size_category': 3,
            'reserved_section_id': 15,  # Has reserved section
            'sectionc_can_change': 0,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(hours=3)
        },
        
        # Order 6 - All ready for delivery
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-666666-1',
            'insert_pin': '666666',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 11,
            'status': 7,  # Ready for delivery
            'tempered': 0,
            'size_category': 1,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(minutes=15)
        },
        {
            'order_uuid': str(uuid4()),
            'order_number': 'ORD-666666-2',
            'insert_pin': '666666',
            'package_pin': generate_pin(),
            'pickup_pin': generate_pin(),
            'section_id': 12,
            'status': 8,  # Ready for delivery
            'tempered': 1,
            'size_category': 2,
            'reserved_section_id': None,
            'sectionc_can_change': 1,
            'price': 0,
            'payment_required': 0,
            'paid_status': 0,
            'expired': 0,
            'age_control_required': 0,
            'age_controlled': 0,
            'created_at': base_time - timedelta(minutes=15)
        }
    ]
    
    insert_query = """
    INSERT INTO order_reservations (
        order_uuid, order_number, insert_pin, package_pin, pickup_pin, section_id, status,
        tempered, size_category, reserved_section_id, sectionc_can_change, price,
        payment_required, paid_status, expired, age_control_required, age_controlled, created_at
    ) VALUES (
        %(order_uuid)s, %(order_number)s, %(insert_pin)s, %(package_pin)s, %(pickup_pin)s,
        %(section_id)s, %(status)s, %(tempered)s, %(size_category)s,
        %(reserved_section_id)s, %(sectionc_can_change)s, %(price)s,
        %(payment_required)s, %(paid_status)s, %(expired)s, %(age_control_required)s,
        %(age_controlled)s, %(created_at)s
    )
    """
    
    try:
        for record in test_records:
            cursor.execute(insert_query, record)
        
        db.commit()
        print(f"✓ Inserted {len(test_records)} test records")
        
        # Print summary
        print("\n📊 Test Data Summary:")
        print("Order 111111: 2 packages, all delivered (status 3) → should complete to status 2")
        print("Order 222222: 2 packages, 1 delivered (status 3), 1 ready (status 7) → needs pickup")
        print("Order 333333: 2 packages, both ready for delivery (status 7,8)")
        print("Order 444444: 1 package, delivered (status 3) → should complete to status 2")
        print("Order 555555: 3 packages, 1 delivered (status 3), 2 ready (status 7,8) → needs pickup")
        print("Order 666666: 2 packages, both ready for delivery (status 7,8)")
        
    except Exception as e:
        print(f"✗ Error inserting records: {e}")
        db.rollback()
    finally:
        cursor.close()
        db.close()

def main():
    """Main function to run the test setup."""
    print("🧪 Setting up test data for order module...")
    print("=" * 50)
    
    # Clear existing data
    clear_order_reservations()
    
    # Insert test data
    insert_test_records()
    
    print("\n✅ Test setup completed!")
    print("\n🔧 Test Scenarios:")
    print("1. Call /order/list-orders to see all orders")
    print("2. Call /order/operator/deliver to start delivery process")
    print("3. Call /order/operator/check-after-delivery to process completed deliveries")
    print("4. Test WebSocket connections for delivery and pickup processes")

if __name__ == "__main__":
    main()
